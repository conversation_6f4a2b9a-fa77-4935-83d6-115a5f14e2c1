'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createSupabaseClient } from '@/lib/supabase'

interface FormData {
  password: string
  confirmPassword: string
}

interface FormErrors {
  password?: string
  confirmPassword?: string
  general?: string
}

export default function SetPasswordPage() {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<FormErrors>({})
  const [message, setMessage] = useState('')
  const [userEmail, setUserEmail] = useState<string>('')
  const router = useRouter()
  const supabase = createSupabaseClient()

  useEffect(() => {
    // Check if user is authenticated and get their email
    const checkUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        // If no user, redirect to login
        router.push('/')
        return
      }
      setUserEmail(user.email || '')
    }
    
    checkUser()
  }, [router, supabase.auth])

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {}

    // Password validation
    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    } else if (formData.password.length > 72) {
      newErrors.password = 'Password must be less than 72 characters'
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))

    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }

    // Clear general message when user starts typing
    if (message) {
      setMessage('')
    }

    // Clear general error when user starts typing
    if (errors.general) {
      setErrors(prev => ({ ...prev, general: undefined }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    setMessage('')
    setErrors({})

    try {
      // Update the user's password
      const { error } = await supabase.auth.updateUser({
        password: formData.password
      })

      if (error) {
        let errorMessage = error.message
        if (error.message.includes('Password should be at least')) {
          errorMessage = 'Password must be at least 6 characters long.'
        }
        setErrors({ general: errorMessage })
      } else {
        setMessage('Password set successfully! Redirecting to dashboard...')
        // Redirect to home page after successful password setup
        setTimeout(() => {
          router.push('/')
        }, 2000)
      }
    } catch (error) {
      console.error('Password setup error:', error)
      setErrors({ general: 'An unexpected error occurred. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Set Your Password
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Welcome to Scaffolding Management! Please set your password to complete your account setup.
          </p>
          {userEmail && (
            <p className="mt-2 text-center text-sm text-gray-500">
              Setting up account for: <span className="font-medium">{userEmail}</span>
            </p>
          )}
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div className="space-y-5">
            {/* Password field */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
                New Password
              </label>
              <input
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                className={`appearance-none relative block w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm transition-colors ${
                  errors.password
                    ? 'border-red-300 placeholder-red-400 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500'
                } text-gray-900`}
                placeholder="Enter your new password"
                autoComplete="new-password"
                disabled={loading}
              />
              {errors.password && (
                <p className="mt-2 text-sm text-red-600">{errors.password}</p>
              )}
            </div>

            {/* Confirm Password field */}
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
                Confirm Password
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className={`appearance-none relative block w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 sm:text-sm transition-colors ${
                  errors.confirmPassword
                    ? 'border-red-300 placeholder-red-400 focus:ring-red-500 focus:border-red-500'
                    : 'border-gray-300 placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500'
                } text-gray-900`}
                placeholder="Confirm your new password"
                autoComplete="new-password"
                disabled={loading}
              />Worker

              {errors.confirmPassword && (
                <p className="mt-2 text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>
          </div>

          {/* Error message */}
          {errors.general && (
            <div className="text-sm text-center p-3 rounded-md bg-red-50 text-red-700 border border-red-200">
              {errors.general}
            </div>
          )}

          {/* Success message */}
          {message && (
            <div className="text-sm text-center p-3 rounded-md bg-green-50 text-green-700 border border-green-200">
              {message}
            </div>
          )}

          {/* Submit button */}
          <div className="pt-2">
            <button
              type="submit"
              disabled={loading}
              className="group relative w-full flex justify-center items-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {loading && (
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              )}
              {loading ? 'Setting Password...' : 'Set Password'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
