import type { <PERSON>ada<PERSON> } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";
import PerformanceMonitor from "@/components/debug/PerformanceMonitor";

export const metadata: Metadata = {
  title: "Scaffolding Management",
  description: "Mobile-first scaffolding management application",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased" suppressHydrationWarning>
        <AuthProvider>
          {children}
          <PerformanceMonitor />
        </AuthProvider>
      </body>
    </html>
  );
}
