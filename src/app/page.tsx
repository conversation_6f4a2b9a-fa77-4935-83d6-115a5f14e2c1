'use client'

import { useAuth } from '@/contexts/AuthContext'
import AuthForm from '@/components/auth/AuthForm'
import WorkerDashboard from '@/components/worker/WorkerDashboard'
import ManagerDashboard from '@/components/manager/ManagerDashboard'

export default function Home() {
  const { user, profile, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-lg text-gray-600">Loading...</div>
        </div>
      </div>
    )
  }

  if (!user || !profile) {
    return <AuthForm />
  }

  if (profile.role === 'manager') {
    return <ManagerDashboard />
  }

  return <WorkerDashboard />
}
