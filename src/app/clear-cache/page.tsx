'use client'

import { useEffect, useState } from 'react'

export default function ClearCachePage() {
  const [cleared, setCleared] = useState(false)

  const clearCache = () => {
    // Clear session storage
    sessionStorage.clear()
    
    // Clear any profile cache keys specifically
    const keys = Object.keys(sessionStorage)
    keys.forEach(key => {
      if (key.startsWith('profile_')) {
        sessionStorage.removeItem(key)
      }
    })
    
    setCleared(true)
    
    // Redirect after a short delay
    setTimeout(() => {
      window.location.href = '/'
    }, 2000)
  }

  useEffect(() => {
    clearCache()
  }, [])

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <div className="text-lg text-gray-600">
          {cleared ? 'Cache cleared! Redirecting...' : 'Clearing cache...'}
        </div>
      </div>
    </div>
  )
}
