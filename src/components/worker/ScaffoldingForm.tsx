'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/contexts/AuthContext'
import { createSupabaseClient } from '@/lib/supabase'

const scaffoldingSchema = z.object({
  system_line: z.string().min(1, 'System line is required'),
  short_iso_number: z.string().min(1, 'Short ISO number is required'),
  start_date: z.string().min(1, 'Start date is required'),
  finish_date: z.string().min(1, 'Finish date is required'),
  status: z.enum(['ongoing', 'finished'], {
    required_error: 'Status is required'
  })
}).refine((data) => {
  const startDate = new Date(data.start_date)
  const finishDate = new Date(data.finish_date)
  return finishDate >= startDate
}, {
  message: 'Finish date must be after or equal to start date',
  path: ['finish_date']
})

type ScaffoldingFormData = z.infer<typeof scaffoldingSchema>

export default function ScaffoldingForm() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [message, setMessage] = useState('')
  const supabase = createSupabaseClient()

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ScaffoldingFormData>({
    resolver: zodResolver(scaffoldingSchema)
  })

  const onSubmit = async (data: ScaffoldingFormData) => {
    if (!user) return

    setLoading(true)
    setMessage('')

    try {
      const { error } = await supabase
        .from('scaffolding_reports')
        .insert({
          user_id: user.id,
          system_line: data.system_line,
          short_iso_number: data.short_iso_number,
          start_date: data.start_date,
          finish_date: data.finish_date,
          status: data.status
        })

      if (error) {
        setMessage('Error submitting report: ' + error.message)
      } else {
        setMessage('Report submitted successfully!')
        reset()
      }
    } catch (error) {
      setMessage('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-md mx-auto">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-6">
          Submit Scaffolding Report
        </h2>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div>
            <label htmlFor="system_line" className="block text-sm font-medium text-gray-700 mb-1">
              System Line
            </label>
            <input
              {...register('system_line')}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter system line"
            />
            {errors.system_line && (
              <p className="mt-1 text-sm text-red-600">{errors.system_line.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="short_iso_number" className="block text-sm font-medium text-gray-700 mb-1">
              Short ISO Number
            </label>
            <input
              {...register('short_iso_number')}
              type="text"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Enter short ISO number"
            />
            {errors.short_iso_number && (
              <p className="mt-1 text-sm text-red-600">{errors.short_iso_number.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              {...register('start_date')}
              type="date"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors.start_date && (
              <p className="mt-1 text-sm text-red-600">{errors.start_date.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="finish_date" className="block text-sm font-medium text-gray-700 mb-1">
              Finish Date
            </label>
            <input
              {...register('finish_date')}
              type="date"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            {errors.finish_date && (
              <p className="mt-1 text-sm text-red-600">{errors.finish_date.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              {...register('status')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Select status</option>
              <option value="ongoing">Ongoing</option>
              <option value="finished">Finished</option>
            </select>
            {errors.status && (
              <p className="mt-1 text-sm text-red-600">{errors.status.message}</p>
            )}
          </div>

          {message && (
            <div className={`text-sm text-center p-3 rounded-md ${
              message.includes('successfully') 
                ? 'bg-green-50 text-green-700 border border-green-200' 
                : 'bg-red-50 text-red-700 border border-red-200'
            }`}>
              {message}
            </div>
          )}

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            {loading ? 'Submitting...' : 'Submit Report'}
          </button>
        </form>
      </div>
    </div>
  )
}
